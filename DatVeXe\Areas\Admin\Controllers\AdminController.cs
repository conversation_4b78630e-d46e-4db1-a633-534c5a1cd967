using Microsoft.AspNetCore.Mvc;
using DatVeXe.Models;
using Microsoft.EntityFrameworkCore;
using DatVeXe.Attributes;

namespace DatVeXe.Areas.Admin.Controllers
{
    [Area("Admin")]
    [AdminAuthorization]
    public class AdminController : Controller
    {
        private readonly DatVeXeContext _context;

        public AdminController(DatVeXeContext context)
        {
            _context = context;
        }

        // Dashboard chính của Admin
        public async Task<IActionResult> Index()
        {
            // Thống kê tổng quan
            var tongNguoiDung = await _context.NguoiDungs.CountAsync();
            var tongVe = await _context.Ves.CountAsync();
            var tongTuyenDuong = await _context.TuyenDuongs.CountAsync();
            var tongXe = await _context.Xes.CountAsync();

            ViewBag.TongNguoiDung = tongNguoiDung;
            ViewBag.TongVe = tongVe;
            ViewBag.TongTuyenDuong = tongTuyenDuong;
            ViewBag.TongXe = tongXe;

            return View();
        }

        // Quản lý người dùng
        public async Task<IActionResult> QuanLyNguoiDung()
        {
            var nguoiDungs = await _context.NguoiDungs.ToListAsync();
            return View(nguoiDungs);
        }

        // Quản lý tuyến đường
        public async Task<IActionResult> QuanLyTuyenDuong()
        {
            var tuyenDuongs = await _context.TuyenDuongs.ToListAsync();
            return View(tuyenDuongs);
        }

        // Quản lý xe
        public async Task<IActionResult> QuanLyXe()
        {
            var xes = await _context.Xes.ToListAsync();
            return View(xes);
        }

        // Quản lý chuyến xe
        public async Task<IActionResult> QuanLyChuyenXe()
        {
            var chuyenXes = await _context.ChuyenXes
                .Include(c => c.Xe)
                .Include(c => c.TuyenDuong)
                .ToListAsync();
            return View(chuyenXes);
        }

        // Quản lý vé
        public async Task<IActionResult> QuanLyVe()
        {
            var ves = await _context.Ves
                .Include(v => v.NguoiDung)
                .Include(v => v.ChuyenXe)
                .ThenInclude(c => c.Xe)
                .Include(v => v.ChuyenXe)
                .ThenInclude(c => c.TuyenDuong)
                .Include(v => v.ChoNgoi)
                .OrderByDescending(v => v.NgayDat)
                .ToListAsync();
            return View(ves);
        }

        // Báo cáo doanh thu
        public async Task<IActionResult> BaoCaoDoanhThu()
        {
            var doanhThuTheoThang = await _context.Ves
                .Where(v => v.TrangThai == TrangThaiVe.DaDat)
                .GroupBy(v => new { v.NgayDat.Year, v.NgayDat.Month })
                .Select(g => new
                {
                    Nam = g.Key.Year,
                    Thang = g.Key.Month,
                    DoanhThu = g.Sum(v => v.GiaVe),
                    SoVe = g.Count()
                })
                .OrderByDescending(x => x.Nam)
                .ThenByDescending(x => x.Thang)
                .ToListAsync();

            return View(doanhThuTheoThang);
        }
    }
}
