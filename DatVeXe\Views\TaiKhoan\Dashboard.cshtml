@{
    ViewData["Title"] = "Dashboard - Tài khoản của tôi";
}

<div class="container-fluid mt-4">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <div class="user-avatar mb-3">
                        <i class="bi bi-person-circle" style="font-size: 4rem; color: #6CABDD;"></i>
                    </div>
                    <h5 class="card-title">@ViewBag.User.HoTen</h5>
                    <p class="text-muted">@ViewBag.User.Email</p>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0"><i class="bi bi-list me-2"></i>Menu</h6>
                </div>
                <div class="list-group list-group-flush">
                    <a href="@Url.Action("Dashboard", "TaiKhoan")" class="list-group-item list-group-item-action active">
                        <i class="bi bi-speedometer2 me-2"></i>Dashboard
                    </a>
                    <a href="@Url.Action("LichSuDatVe", "TaiKhoan")" class="list-group-item list-group-item-action">
                        <i class="bi bi-clock-history me-2"></i>Lịch sử đặt vé
                    </a>
                    <a href="@Url.Action("Profile", "TaiKhoan")" class="list-group-item list-group-item-action">
                        <i class="bi bi-person-gear me-2"></i>Cập nhật thông tin
                    </a>
                    <a href="@Url.Action("Ve", "Create")" class="list-group-item list-group-item-action">
                        <i class="bi bi-plus-circle me-2"></i>Đặt vé mới
                    </a>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9">
            <!-- Welcome Section -->
            <div class="card mb-4">
                <div class="card-body">
                    <h4 class="card-title">
                        <i class="bi bi-house-heart me-2 text-primary"></i>
                        Chào mừng trở lại, @ViewBag.User.HoTen!
                    </h4>
                    <p class="text-muted">Quản lý vé xe và thông tin cá nhân của bạn tại đây.</p>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-center border-primary">
                        <div class="card-body">
                            <i class="bi bi-ticket-perforated text-primary" style="font-size: 2rem;"></i>
                            <h4 class="mt-2">@ViewBag.TotalTickets</h4>
                            <p class="text-muted">Tổng vé đã đặt</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center border-success">
                        <div class="card-body">
                            <i class="bi bi-calendar-check text-success" style="font-size: 2rem;"></i>
                            <h4 class="mt-2">@ViewBag.UpcomingTrips</h4>
                            <p class="text-muted">Chuyến sắp tới</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center border-info">
                        <div class="card-body">
                            <i class="bi bi-check-circle text-info" style="font-size: 2rem;"></i>
                            <h4 class="mt-2">@ViewBag.CompletedTrips</h4>
                            <p class="text-muted">Đã hoàn thành</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center border-warning">
                        <div class="card-body">
                            <i class="bi bi-currency-dollar text-warning" style="font-size: 2rem;"></i>
                            <h4 class="mt-2">@ViewBag.TotalSpent.ToString("N0")</h4>
                            <p class="text-muted">Tổng chi tiêu (VNĐ)</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Tickets -->
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="bi bi-clock me-2"></i>
                        Vé sắp tới
                    </h5>
                </div>
                <div class="card-body">
                    @if (ViewBag.RecentTickets != null && ((List<DatVeXe.Models.Ve>)ViewBag.RecentTickets).Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Mã vé</th>
                                        <th>Tuyến đường</th>
                                        <th>Ngày khởi hành</th>
                                        <th>Giá vé</th>
                                        <th>Trạng thái</th>
                                        <th>Thao tác</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var ticket in (List<DatVeXe.Models.Ve>)ViewBag.RecentTickets)
                                    {
                                        <tr>
                                            <td>
                                                <strong class="text-primary">@ticket.MaVe</strong>
                                            </td>
                                            <td>
                                                <i class="bi bi-geo-alt me-1"></i>
                                                @ticket.ChuyenXe.DiemDiDisplay → @ticket.ChuyenXe.DiemDenDisplay
                                            </td>
                                            <td>
                                                <i class="bi bi-calendar me-1"></i>
                                                @ticket.ChuyenXe.NgayKhoiHanh.ToString("dd/MM/yyyy HH:mm")
                                            </td>
                                            <td>
                                                <span class="text-success fw-bold">@ticket.GiaVe.ToString("N0") VNĐ</span>
                                            </td>
                                            <td>
                                                @if (ticket.TrangThai == DatVeXe.Models.TrangThaiVe.DaDat)
                                                {
                                                    <span class="badge bg-success">Đã đặt</span>
                                                }
                                                else if (ticket.TrangThai == DatVeXe.Models.TrangThaiVe.DaHuy)
                                                {
                                                    <span class="badge bg-danger">Đã hủy</span>
                                                }
                                            </td>
                                            <td>
                                                <a href="@Url.Action("Details", "Ve", new { id = ticket.VeId })" 
                                                   class="btn btn-sm btn-outline-primary">
                                                    <i class="bi bi-eye me-1"></i>Xem
                                                </a>
                                                @if (ticket.TrangThai == DatVeXe.Models.TrangThaiVe.DaDat && 
                                                     ticket.ChuyenXe.NgayKhoiHanh > DateTime.Now.AddHours(2))
                                                {
                                                    <a href="@Url.Action("Cancel", "Ve", new { id = ticket.VeId })" 
                                                       class="btn btn-sm btn-outline-danger">
                                                        <i class="bi bi-x-circle me-1"></i>Hủy
                                                    </a>
                                                }
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                        <div class="text-center mt-3">
                            <a href="@Url.Action("LichSuDatVe", "TaiKhoan")" class="btn btn-primary">
                                <i class="bi bi-list me-1"></i>Xem tất cả lịch sử
                            </a>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-4">
                            <i class="bi bi-ticket-perforated text-muted" style="font-size: 3rem;"></i>
                            <h5 class="mt-3 text-muted">Chưa có vé nào</h5>
                            <p class="text-muted">Bạn chưa đặt vé nào. Hãy đặt vé đầu tiên của bạn!</p>
                            <a href="@Url.Action("Create", "Ve")" class="btn btn-primary">
                                <i class="bi bi-plus-circle me-1"></i>Đặt vé ngay
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .card {
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border: none;
    }
    
    .user-avatar {
        margin-bottom: 1rem;
    }
    
    .list-group-item {
        border: none;
        border-bottom: 1px solid #eee;
    }
    
    .list-group-item:hover {
        background-color: #f8f9fa;
    }
    
    .list-group-item.active {
        background-color: #6CABDD;
        border-color: #6CABDD;
    }
    
    .border-primary { border-color: #6CABDD !important; }
    .text-primary { color: #6CABDD !important; }
    .bg-primary { background-color: #6CABDD !important; }
    .btn-primary { background-color: #6CABDD; border-color: #6CABDD; }
    .btn-primary:hover { background-color: #5a9bc4; border-color: #5a9bc4; }
</style>
