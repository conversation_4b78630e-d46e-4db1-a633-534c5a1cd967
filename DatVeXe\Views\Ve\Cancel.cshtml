@model DatVeXe.Models.Ve
@{
    ViewData["Title"] = "Hủy vé";
}

<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-danger text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-exclamation-triangle"></i> Xác nhận hủy vé
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-info-circle"></i>
                        <strong>Lưu ý:</strong> Việc hủy vé sẽ không thể hoàn tác. Vui lòng kiểm tra kỹ thông tin trước khi xác nhận.
                    </div>

                    <h6 class="mb-3">Thông tin vé cần hủy:</h6>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Mã vé:</strong></td>
                                    <td>#@Model.VeId</td>
                                </tr>
                                <tr>
                                    <td><strong>Tên khách hàng:</strong></td>
                                    <td>@Model.TenKhach</td>
                                </tr>
                                <tr>
                                    <td><strong>Số điện thoại:</strong></td>
                                    <td>@Model.SoDienThoai</td>
                                </tr>
                                <tr>
                                    <td><strong>Ngày đặt:</strong></td>
                                    <td>@Model.NgayDat.ToString("dd/MM/yyyy HH:mm")</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Tuyến đường:</strong></td>
                                    <td>@Model.ChuyenXe.DiemDi → @Model.ChuyenXe.DiemDen</td>
                                </tr>
                                <tr>
                                    <td><strong>Ngày khởi hành:</strong></td>
                                    <td>@Model.ChuyenXe.NgayKhoiHanh.ToString("dd/MM/yyyy HH:mm")</td>
                                </tr>
                                <tr>
                                    <td><strong>Xe:</strong></td>
                                    <td>@Model.ChuyenXe.Xe.BienSo (@Model.ChuyenXe.Xe.LoaiXe)</td>
                                </tr>
                                <tr>
                                    <td><strong>Giá vé:</strong></td>
                                    <td><span class="text-success fw-bold">@Model.ChuyenXe.Gia.ToString("N0") VNĐ</span></td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    @{
                        var timeUntilDeparture = Model.ChuyenXe.NgayKhoiHanh - DateTime.Now;
                        var canCancel = timeUntilDeparture.TotalHours > 2;
                    }

                    @if (!canCancel)
                    {
                        <div class="alert alert-danger">
                            <i class="fas fa-times-circle"></i>
                            <strong>Không thể hủy vé:</strong> Chỉ có thể hủy vé trước 2 giờ khởi hành.
                            Thời gian còn lại: @timeUntilDeparture.Hours giờ @timeUntilDeparture.Minutes phút.
                        </div>
                    }
                    else
                    {
                        <div class="alert alert-info">
                            <i class="fas fa-clock"></i>
                            <strong>Thời gian còn lại để khởi hành:</strong> 
                            @if (timeUntilDeparture.Days > 0)
                            {
                                <span>@timeUntilDeparture.Days ngày </span>
                            }
                            @timeUntilDeparture.Hours giờ @timeUntilDeparture.Minutes phút
                        </div>
                    }
                </div>
                <div class="card-footer">
                    <div class="d-flex justify-content-between">
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Quay lại
                        </a>
                        
                        @if (canCancel)
                        {
                            <form asp-action="Cancel" method="post" class="d-inline">
                                <input type="hidden" asp-for="VeId" />
                                <button type="submit" class="btn btn-danger" onclick="return confirm('Bạn có chắc chắn muốn hủy vé này không?')">
                                    <i class="fas fa-trash"></i> Xác nhận hủy vé
                                </button>
                            </form>
                        }
                        else
                        {
                            <button type="button" class="btn btn-danger" disabled>
                                <i class="fas fa-ban"></i> Không thể hủy
                            </button>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        .table-borderless td {
            padding: 0.5rem 0.75rem;
            border: none;
        }
        .table-borderless td:first-child {
            width: 40%;
            color: #6c757d;
        }
        .card {
            border: none;
            border-radius: 10px;
        }
        .card-header {
            border-radius: 10px 10px 0 0 !important;
        }
    </style>
}
