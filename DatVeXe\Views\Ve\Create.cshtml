@model DatVeXe.Models.Ve
@{
    ViewData["Title"] = "Đặt vé mới";
}

<!-- Hero Section with Manchester City Bus Image -->
<div class="booking-hero-section">
    <div class="booking-hero-overlay">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="booking-hero-title">
                        <i class="bi bi-ticket-perforated me-3"></i>
                        Đặt vé xe khách
                    </h1>
                    <p class="booking-hero-subtitle">
                        Chọn chuyến xe phù hợp và đặt vé một cách nhanh chóng, tiện lợi
                    </p>
                </div>
                <div class="col-lg-4 text-end">
                    <div class="booking-progress">
                        <div class="progress-step active">
                            <div class="step-number">1</div>
                            <div class="step-label">Chọ<PERSON> chuyến</div>
                        </div>
                        <div class="progress-line"></div>
                        <div class="progress-step">
                            <div class="step-number">2</div>
                            <div class="step-label">Thông tin</div>
                        </div>
                        <div class="progress-line"></div>
                        <div class="progress-step">
                            <div class="step-number">3</div>
                            <div class="step-label">Xác nhận</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid px-5 py-5">
    <!-- Trip Search Section -->
    <div class="trip-search-section mb-5">
        <div class="card border-0 shadow-sm">
            <div class="card-header" style="background-color: #3498db; color: white;">
                <h5 class="card-title mb-0">
                    <i class="bi bi-search me-2"></i>Tìm kiếm chuyến xe
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">Điểm đi</label>
                        <select class="form-select" id="filterDiemDi">
                            <option value="">Tất cả</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Điểm đến</label>
                        <select class="form-select" id="filterDiemDen">
                            <option value="">Tất cả</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Ngày khởi hành</label>
                        <input type="date" class="form-control" id="filterNgayKhoiHanh" min="@DateTime.Now.ToString("yyyy-MM-dd")">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <button type="button" class="btn w-100" id="searchTripsBtn" style="background-color: #3498db; border-color: #3498db; color: white;">
                            <i class="bi bi-search me-2"></i>Tìm kiếm
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Booking Form -->
    <div class="row">
        <!-- Trip Selection -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header" style="background-color: #f8f9fa; color: #2c3e50; border-bottom: 1px solid #e9ecef;">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-bus-front me-2" style="color: #3498db;"></i>Chọn chuyến xe
                    </h5>
                </div>
                <div class="card-body">
                    <form asp-action="Create" method="post" id="createForm" novalidate>
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger" style="display:none;"></div>

                        <div class="mb-4">
                            <label asp-for="ChuyenXeId" class="form-label required">Chuyến xe</label>
                            <select asp-for="ChuyenXeId" asp-items="ViewBag.ChuyenXeList" class="form-select form-select-lg" id="chuyenXeSelect">
                                <option value="">-- Chọn chuyến xe --</option>
                            </select>
                            <span asp-validation-for="ChuyenXeId" class="text-danger"></span>
                        </div>

                        <!-- Trip Information Display -->
                        <div id="chuyenXeInfo" class="trip-info-card" style="display: none;">
                            <div class="card border-primary">
                                <div class="card-header" style="background-color: #3498db; color: white;">
                                    <h6 class="mb-0">
                                        <i class="bi bi-info-circle me-2"></i>Thông tin chuyến xe
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div id="chuyenXeDetails"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Customer Information Section -->
                        <div id="customerInfoSection" style="display: none;">
                            <div class="card border-success mt-4">
                                <div class="card-header" style="background-color: #27ae60; color: white;">
                                    <h6 class="mb-0">
                                        <i class="bi bi-person me-2"></i>Thông tin khách hàng
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label asp-for="TenKhach" class="form-label required"></label>
                                                <input asp-for="TenKhach" class="form-control" placeholder="Nhập tên khách hàng" />
                                                <span asp-validation-for="TenKhach" class="text-danger"></span>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label asp-for="SoDienThoai" class="form-label required"></label>
                                                <input asp-for="SoDienThoai" class="form-control" placeholder="Nhập số điện thoại (VD: 0912345678)" />
                                                <span asp-validation-for="SoDienThoai" class="text-danger"></span>
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label asp-for="Email" class="form-label">
                                                    <i class="bi bi-envelope me-1"></i>Email (để nhận xác nhận đặt vé)
                                                </label>
                                                <input asp-for="Email" class="form-control" placeholder="Nhập email để nhận xác nhận đặt vé (không bắt buộc)" />
                                                <span asp-validation-for="Email" class="text-danger"></span>
                                                <div class="form-text">
                                                    <i class="bi bi-info-circle me-1"></i>
                                                    Nếu cung cấp email, bạn sẽ nhận được email xác nhận đặt vé với thông tin chi tiết.
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="mt-4">
                            <div class="d-flex justify-content-between">
                                <a asp-action="Index" class="btn btn-outline-secondary btn-lg">
                                    <i class="bi bi-arrow-left me-2"></i>Quay lại
                                </a>
                                <button type="submit" class="btn btn-primary btn-lg" id="submitBtn" disabled>
                                    <i class="bi bi-check-circle me-2"></i>Đặt vé ngay
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        <!-- Booking Summary Sidebar -->
        <div class="col-lg-4">
            <div class="booking-summary-card">
                <div class="card border-0 shadow-sm sticky-top">
                    <div class="card-header" style="background-color: #f39c12; color: white;">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-receipt me-2"></i>Tóm tắt đặt vé
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="bookingSummary">
                            <div class="text-center text-muted py-4">
                                <i class="bi bi-info-circle fs-1 mb-3"></i>
                                <p>Vui lòng chọn chuyến xe để xem thông tin đặt vé</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Tips -->
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header" style="background-color: #9b59b6; color: white;">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-lightbulb me-2"></i>Lưu ý quan trọng
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="bi bi-check-circle me-2" style="color: #27ae60;"></i>
                            Vé có thể hủy trước 2 giờ khởi hành
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-check-circle me-2" style="color: #27ae60;"></i>
                            Mang theo CMND/CCCD khi lên xe
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-check-circle me-2" style="color: #27ae60;"></i>
                            Có mặt tại bến trước 15 phút
                        </li>
                        <li class="mb-0">
                            <i class="bi bi-check-circle me-2" style="color: #27ae60;"></i>
                            Liên hệ hotline: 1900-xxxx
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        /* Booking Hero Section - Clean Design */
        .booking-hero-section {
            background-color: #2c3e50;
            padding: 3rem 0 2rem;
            margin-bottom: 2rem;
            border-bottom: 4px solid #3498db;
        }

        .booking-hero-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #ffffff;
            margin-bottom: 1rem;
        }

        .booking-hero-subtitle {
            font-size: 1.2rem;
            color: #ecf0f1;
            margin-bottom: 0;
            font-weight: 400;
        }

        /* Progress Steps */
        .booking-progress {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
        }

        .progress-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }

        .step-number {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 1.2rem;
            margin-bottom: 0.75rem;
            transition: all 0.3s ease;
            border: 3px solid transparent;
        }

        .progress-step.active .step-number {
            background-color: #3498db;
            color: #ffffff;
            border-color: #ffffff;
        }

        .step-label {
            font-size: 0.9rem;
            color: #bdc3c7;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .progress-step.active .step-label {
            color: #3498db;
            font-weight: 600;
        }

        .progress-line {
            width: 30px;
            height: 2px;
            background-color: #34495e;
            margin-top: -20px;
        }

        /* Form Enhancements */
        .required::after {
            content: " *";
            color: #dc3545;
            font-weight: bold;
        }

        .form-select-lg, .form-control-lg {
            padding: 1rem 1.25rem;
            font-size: 1.2rem;
            border-radius: 8px;
            border: 2px solid #e9ecef;
        }

        .form-control, .form-select {
            padding: 0.75rem 1rem;
            font-size: 1.1rem;
            border-radius: 6px;
            border: 2px solid #e9ecef;
        }

        .trip-info-card {
            animation: slideDown 0.4s ease-out;
            margin-top: 2rem;
        }

        @@keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Booking Summary */
        .booking-summary-card .sticky-top {
            top: 2rem;
        }

        .summary-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid #eee;
        }

        .summary-item:last-child {
            border-bottom: none;
            font-weight: 600;
            font-size: 1.1rem;
            color: #0a2640;
        }

        .summary-label {
            color: #666;
            font-weight: 500;
        }

        .summary-value {
            font-weight: 600;
            color: #0a2640;
        }

        .price-highlight {
            color: #27ae60 !important;
            font-size: 1.2rem;
            font-weight: 700;
        }

        /* Validation Styles */
        .validation-summary-errors {
            display: block !important;
        }

        .validation-summary-errors ul {
            list-style: none;
            padding-left: 0;
            margin-bottom: 0;
        }

        .field-validation-error {
            font-size: 0.875rem;
            margin-top: 0.25rem;
            display: block;
        }

        /* Info Items Styling */
        .info-item {
            padding: 0.5rem 0;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
        }

        .info-item:last-child {
            border-bottom: none;
        }

        /* Enhanced Card Styling - Clean Design */
        .card {
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid #e9ecef;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: box-shadow 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .card-header {
            border-bottom: 1px solid #e9ecef;
            font-weight: 600;
            font-size: 1.1rem;
            padding: 1rem 1.5rem;
        }

        .card-body {
            padding: 1.5rem;
        }

        /* Button Enhancements - Clean Design */
        .btn-lg {
            padding: 0.875rem 2rem;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background-color: #3498db;
            border-color: #3498db;
            color: #ffffff;
        }

        .btn-primary:hover {
            background-color: #2980b9;
            border-color: #2980b9;
            transform: translateY(-1px);
        }

        .btn-primary:disabled {
            background-color: #95a5a6;
            border-color: #95a5a6;
            transform: none;
        }

        .btn-outline-secondary {
            border: 1px solid #6c757d;
            color: #6c757d;
            font-weight: 500;
        }

        .btn-outline-secondary:hover {
            background-color: #6c757d;
            border-color: #6c757d;
            color: #ffffff;
        }

        /* Form Control Enhancements */
        .form-control:focus, .form-select:focus {
            border-color: #3498db;
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }

        /* Alert Enhancements */
        .alert {
            border-radius: 10px;
            border: none;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        /* Modal Enhancements */
        .modal-content {
            border-radius: 15px;
            border: none;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .modal-header {
            border-bottom: 1px solid rgba(0,0,0,0.1);
            border-radius: 15px 15px 0 0;
        }

        /* Loading Spinner */
        .spinner-border {
            width: 2rem;
            height: 2rem;
        }

        /* Hover Effects */
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        /* Desktop-optimized Design */
        .booking-hero-title {
            font-size: 2.8rem;
        }

        .booking-progress {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1.5rem;
        }

        .progress-line {
            width: 50px;
            height: 3px;
            background: rgba(255, 255, 255, 0.3);
            margin-top: -20px;
        }

        .booking-summary-card .sticky-top {
            position: sticky;
            top: 2rem;
        }

        .btn-lg {
            min-width: 150px;
        }

        /* Animation for smooth transitions */
        .trip-info-card, #customerInfoSection {
            transition: all 0.4s ease;
        }

        /* Success state styling */
        .progress-step.active .step-number {
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.3);
        }

        /* Desktop-specific optimizations */
        .container-fluid {
            max-width: 1400px;
            margin: 0 auto;
        }

        .row {
            margin-left: -20px;
            margin-right: -20px;
        }

        .col-lg-8, .col-lg-4 {
            padding-left: 20px;
            padding-right: 20px;
        }

        /* Enhanced hover effects for desktop */
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
        }

        /* Larger spacing for desktop */
        .mb-4 {
            margin-bottom: 2.5rem !important;
        }

        .mb-5 {
            margin-bottom: 3.5rem !important;
        }

        .py-5 {
            padding-top: 4rem !important;
            padding-bottom: 4rem !important;
        }
    </style>
}

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script>
        $(document).ready(function() {
            // Initialize page
            initializePage();
            loadFilterOptions();

            // Auto-fill customer info if logged in
            @if (Context.Session.GetString("UserName") != null)
            {
                <text>
                $('#TenKhach').val('@Context.Session.GetString("UserName")');
                </text>
            }

            // Hiển thị validation summary nếu có lỗi
            if ($('.validation-summary-errors').length > 0 || $('.field-validation-error').length > 0) {
                $('.validation-summary-errors').closest('.alert').show();
            }

            // Progress step management
            function updateProgressStep(step) {
                $('.progress-step').removeClass('active');
                for (let i = 1; i <= step; i++) {
                    $(`.progress-step:nth-child(${i * 2 - 1})`).addClass('active');
                }
            }

            // Trip search functionality
            $('#searchTripsBtn').click(function() {
                var diemDi = $('#filterDiemDi').val();
                var diemDen = $('#filterDiemDen').val();
                var ngayKhoiHanh = $('#filterNgayKhoiHanh').val();

                filterTrips(diemDi, diemDen, ngayKhoiHanh);
            });

            // Format số điện thoại
            $('#SoDienThoai').on('input', function() {
                var value = $(this).val().replace(/\D/g, '');
                if (value.length > 0 && value[0] !== '0') {
                    value = '0' + value;
                }
                if (value.length > 10) {
                    value = value.substr(0, 10);
                }
                $(this).val(value);
            });

            // Enhanced trip selection handling
            $('#chuyenXeSelect').change(function() {
                var chuyenXeId = $(this).val();
                if (chuyenXeId) {
                    updateProgressStep(2);
                    loadTripInfo(chuyenXeId);
                } else {
                    updateProgressStep(1);
                    hideTripInfo();
                    hideCustomerInfo();
                    updateBookingSummary(null);
                }
            });

            // Customer info validation
            $('#TenKhach, #SoDienThoai').on('input', function() {
                validateCustomerInfo();
            });

            // Form submission with confirmation
            $('#createForm').on('submit', function(e) {
                e.preventDefault();

                if (!$(this).valid()) {
                    $('.validation-summary-errors').closest('.alert').show();
                    scrollToFirstError();
                    return;
                }

                showBookingConfirmation();
            });

            function initializePage() {
                $('#customerInfoSection').hide();
                $('#submitBtn').prop('disabled', true);
                updateBookingSummary(null);
            }

            function loadFilterOptions() {
                // Load unique departure and destination points
                var options = $('#chuyenXeSelect option');
                var diemDiSet = new Set();
                var diemDenSet = new Set();

                options.each(function() {
                    var text = $(this).text();
                    if (text.includes(' - ')) {
                        var parts = text.split(' - ');
                        if (parts.length >= 2) {
                            diemDiSet.add(parts[0]);
                            diemDenSet.add(parts[1].split(' (')[0]);
                        }
                    }
                });

                // Populate filter dropdowns
                diemDiSet.forEach(function(diemDi) {
                    $('#filterDiemDi').append(`<option value="${diemDi}">${diemDi}</option>`);
                });

                diemDenSet.forEach(function(diemDen) {
                    $('#filterDiemDen').append(`<option value="${diemDen}">${diemDen}</option>`);
                });
            }

            function filterTrips(diemDi, diemDen, ngayKhoiHanh) {
                var options = $('#chuyenXeSelect option');
                var hasVisibleOptions = false;

                options.each(function() {
                    var $option = $(this);
                    var text = $option.text();
                    var value = $option.val();

                    if (value === '') {
                        $option.show();
                        return;
                    }

                    var show = true;

                    if (diemDi && !text.includes(diemDi + ' - ')) {
                        show = false;
                    }

                    if (diemDen && !text.includes(' - ' + diemDen)) {
                        show = false;
                    }

                    if (ngayKhoiHanh) {
                        var dateMatch = text.match(/\((\d{2}\/\d{2}\/\d{4})/);
                        if (dateMatch) {
                            var tripDate = dateMatch[1];
                            var filterDate = new Date(ngayKhoiHanh).toLocaleDateString('vi-VN');
                            if (tripDate !== filterDate) {
                                show = false;
                            }
                        }
                    }

                    if (show) {
                        $option.show();
                        hasVisibleOptions = true;
                    } else {
                        $option.hide();
                    }
                });

                // Reset selection if current option is hidden
                if ($('#chuyenXeSelect option:selected').is(':hidden')) {
                    $('#chuyenXeSelect').val('').trigger('change');
                }

                // Show message if no trips found
                if (!hasVisibleOptions) {
                    showAlert('Không tìm thấy chuyến xe phù hợp với tiêu chí tìm kiếm.', 'warning');
                }
            }

            function loadTripInfo(chuyenXeId) {
                $.ajax({
                    url: '@Url.Action("GetChuyenXeInfo", "Ve")',
                    type: 'GET',
                    data: { id: chuyenXeId },
                    beforeSend: function() {
                        showLoadingSpinner('#chuyenXeDetails');
                    },
                    success: function(data) {
                        if (data.success) {
                            displayTripInfo(data.chuyenXe);
                            updateBookingSummary(data.chuyenXe);
                            showCustomerInfo();
                        } else {
                            showAlert(data.message || 'Không thể tải thông tin chuyến xe', 'danger');
                            hideTripInfo();
                        }
                    },
                    error: function() {
                        showAlert('Có lỗi xảy ra khi tải thông tin chuyến xe', 'danger');
                        hideTripInfo();
                    }
                });
            }

            function displayTripInfo(info) {
                var soGheTrong = info.soGhe - info.soVeDaBan;
                var statusClass = soGheTrong > 0 ? 'text-success' : 'text-danger';
                var statusText = soGheTrong > 0 ? 'Còn chỗ' : 'Hết chỗ';
                var statusIcon = soGheTrong > 0 ? 'bi-check-circle' : 'bi-x-circle';

                var html = `
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="info-item">
                                <i class="bi bi-geo-alt text-primary me-2"></i>
                                <strong>Tuyến:</strong> ${info.diemDi} → ${info.diemDen}
                            </div>
                            <div class="info-item">
                                <i class="bi bi-calendar text-primary me-2"></i>
                                <strong>Ngày khởi hành:</strong> ${info.ngayKhoiHanh}
                            </div>
                            <div class="info-item">
                                <i class="bi bi-bus-front text-primary me-2"></i>
                                <strong>Xe:</strong> ${info.bienSo} (${info.loaiXe})
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <i class="bi bi-currency-dollar text-success me-2"></i>
                                <strong>Giá vé:</strong> <span class="text-success fw-bold">${info.gia} VNĐ</span>
                            </div>
                            <div class="info-item">
                                <i class="bi bi-people text-primary me-2"></i>
                                <strong>Số ghế:</strong> ${info.soGhe} chỗ
                            </div>
                            <div class="info-item">
                                <i class="bi ${statusIcon} ${statusClass} me-2"></i>
                                <strong>Trạng thái:</strong> <span class="${statusClass} fw-bold">${statusText} (${soGheTrong} chỗ trống)</span>
                            </div>
                        </div>
                    </div>
                `;

                $('#chuyenXeDetails').html(html);
                $('#chuyenXeInfo').slideDown(300);

                // Update submit button state
                if (soGheTrong <= 0 || info.daKhoiHanh) {
                    $('#submitBtn').prop('disabled', true).html('<i class="bi bi-x-circle me-2"></i>Không thể đặt vé');
                } else {
                    validateCustomerInfo();
                }
            }

            function hideTripInfo() {
                $('#chuyenXeInfo').slideUp(300);
            }

            function showCustomerInfo() {
                $('#customerInfoSection').slideDown(300);
                updateProgressStep(2);
            }

            function hideCustomerInfo() {
                $('#customerInfoSection').slideUp(300);
            }

            function validateCustomerInfo() {
                var tenKhach = $('#TenKhach').val().trim();
                var soDienThoai = $('#SoDienThoai').val().trim();
                var chuyenXeId = $('#chuyenXeSelect').val();

                var isValid = tenKhach.length >= 2 &&
                             soDienThoai.length === 10 &&
                             soDienThoai.match(/^0\d{9}$/) &&
                             chuyenXeId;

                if (isValid) {
                    $('#submitBtn').prop('disabled', false).html('<i class="bi bi-check-circle me-2"></i>Đặt vé ngay');
                    updateProgressStep(3);
                } else {
                    $('#submitBtn').prop('disabled', true).html('<i class="bi bi-check-circle me-2"></i>Đặt vé ngay');
                }
            }

            function updateBookingSummary(tripInfo) {
                if (!tripInfo) {
                    $('#bookingSummary').html(`
                        <div class="text-center text-muted py-4">
                            <i class="bi bi-info-circle fs-1 mb-3"></i>
                            <p>Vui lòng chọn chuyến xe để xem thông tin đặt vé</p>
                        </div>
                    `);
                    return;
                }

                var soGheTrong = tripInfo.soGhe - tripInfo.soVeDaBan;
                var html = `
                    <div class="summary-item">
                        <span class="summary-label">Tuyến xe:</span>
                        <span class="summary-value">${tripInfo.diemDi} → ${tripInfo.diemDen}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">Ngày khởi hành:</span>
                        <span class="summary-value">${tripInfo.ngayKhoiHanh}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">Loại xe:</span>
                        <span class="summary-value">${tripInfo.loaiXe}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">Biển số:</span>
                        <span class="summary-value">${tripInfo.bienSo}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">Chỗ trống:</span>
                        <span class="summary-value ${soGheTrong > 0 ? 'text-success' : 'text-danger'}">${soGheTrong} chỗ</span>
                    </div>
                    <hr>
                    <div class="summary-item">
                        <span class="summary-label">Tổng tiền:</span>
                        <span class="summary-value price-highlight">${tripInfo.gia} VNĐ</span>
                    </div>
                `;

                $('#bookingSummary').html(html);
            }

            function showBookingConfirmation() {
                var tripInfo = {
                    tuyen: $('#chuyenXeSelect option:selected').text(),
                    tenKhach: $('#TenKhach').val(),
                    soDienThoai: $('#SoDienThoai').val()
                };

                var confirmHtml = `
                    <div class="modal fade" id="confirmModal" tabindex="-1">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header bg-primary text-white">
                                    <h5 class="modal-title">
                                        <i class="bi bi-check-circle me-2"></i>Xác nhận đặt vé
                                    </h5>
                                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                                </div>
                                <div class="modal-body">
                                    <p class="mb-3">Vui lòng kiểm tra lại thông tin đặt vé:</p>
                                    <div class="card">
                                        <div class="card-body">
                                            <p><strong>Chuyến xe:</strong> ${tripInfo.tuyen}</p>
                                            <p><strong>Tên khách hàng:</strong> ${tripInfo.tenKhach}</p>
                                            <p><strong>Số điện thoại:</strong> ${tripInfo.soDienThoai}</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                                    <button type="button" class="btn btn-primary" id="confirmBookingBtn">
                                        <i class="bi bi-check-circle me-2"></i>Xác nhận đặt vé
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                $('body').append(confirmHtml);
                $('#confirmModal').modal('show');

                $('#confirmBookingBtn').click(function() {
                    $('#confirmModal').modal('hide');
                    $('#createForm')[0].submit();
                });

                $('#confirmModal').on('hidden.bs.modal', function() {
                    $(this).remove();
                });
            }

            function showLoadingSpinner(selector) {
                $(selector).html(`
                    <div class="text-center py-3">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Đang tải...</span>
                        </div>
                    </div>
                `);
            }

            function showAlert(message, type) {
                var alertHtml = `
                    <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                        ${message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                `;

                $('.container').prepend(alertHtml);

                setTimeout(function() {
                    $('.alert').fadeOut();
                }, 5000);
            }

            function scrollToFirstError() {
                var firstError = $('.field-validation-error:visible').first();
                if (firstError.length > 0) {
                    $('html, body').animate({
                        scrollTop: firstError.offset().top - 100
                    }, 300);
                }
            }
        });
    </script>
}
