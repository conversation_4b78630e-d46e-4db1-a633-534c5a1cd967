@model List<DatVeXe.Models.Ve>

@if (Model != null && Model.Any())
{
    <div class="table-responsive">
        <table class="table table-hover">
            <thead class="table-light">
                <tr>
                    <th>Mã vé</th>
                    <th><PERSON><PERSON><PERSON><PERSON> đ<PERSON></th>
                    <th>Ngày đặt</th>
                    <th>Ngày khởi hành</th>
                    <th>Xe</th>
                    <th>Giá vé</th>
                    <th>Trạng thái</th>
                    <th>Thao tác</th>
                </tr>
            </thead>
            <tbody>
                @foreach (var ticket in Model)
                {
                    <tr>
                        <td>
                            <strong class="text-primary">@ticket.MaVe</strong>
                            <br>
                            <small class="text-muted">@ticket.TenKhach</small>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <i class="bi bi-geo-alt me-2 text-primary"></i>
                                <div>
                                    <strong>@ticket.ChuyenXe.DiemDiDisplay</strong>
                                    <br>
                                    <i class="bi bi-arrow-down text-muted"></i>
                                    <br>
                                    <strong>@ticket.ChuyenXe.DiemDenDisplay</strong>
                                </div>
                            </div>
                        </td>
                        <td>
                            <i class="bi bi-calendar-plus me-1 text-success"></i>
                            @ticket.NgayDat.ToString("dd/MM/yyyy")
                            <br>
                            <small class="text-muted">@ticket.NgayDat.ToString("HH:mm")</small>
                        </td>
                        <td>
                            <i class="bi bi-calendar-event me-1 text-info"></i>
                            @ticket.ChuyenXe.NgayKhoiHanh.ToString("dd/MM/yyyy")
                            <br>
                            <small class="text-muted">@ticket.ChuyenXe.NgayKhoiHanh.ToString("HH:mm")</small>
                            @if (ticket.ChuyenXe.NgayKhoiHanh > DateTime.Now)
                            {
                                <br><small class="badge bg-info">Sắp tới</small>
                            }
                            else if (ticket.TrangThai == DatVeXe.Models.TrangThaiVe.DaDat)
                            {
                                <br><small class="badge bg-success">Đã đi</small>
                            }
                        </td>
                        <td>
                            <i class="bi bi-bus-front me-1 text-warning"></i>
                            @ticket.ChuyenXe.Xe.BienSo
                            <br>
                            <small class="text-muted">@ticket.ChuyenXe.Xe.LoaiXe</small>
                        </td>
                        <td>
                            <span class="text-success fw-bold">@ticket.GiaVe.ToString("N0") VNĐ</span>
                        </td>
                        <td>
                            @if (ticket.TrangThai == DatVeXe.Models.TrangThaiVe.DaDat)
                            {
                                <span class="badge bg-success">
                                    <i class="bi bi-check-circle me-1"></i>Đã đặt
                                </span>
                            }
                            else if (ticket.TrangThai == DatVeXe.Models.TrangThaiVe.DaHuy)
                            {
                                <span class="badge bg-danger">
                                    <i class="bi bi-x-circle me-1"></i>Đã hủy
                                </span>
                                @if (ticket.NgayHuy.HasValue)
                                {
                                    <br><small class="text-muted">@ticket.NgayHuy.Value.ToString("dd/MM/yyyy")</small>
                                }
                            }
                        </td>
                        <td>
                            <div class="btn-group-vertical" role="group">
                                <a href="@Url.Action("Details", "Ve", new { id = ticket.VeId })" 
                                   class="btn btn-sm btn-outline-primary mb-1">
                                    <i class="bi bi-eye me-1"></i>Xem chi tiết
                                </a>
                                
                                @if (ticket.TrangThai == DatVeXe.Models.TrangThaiVe.DaDat)
                                {
                                    @if (ticket.ChuyenXe.NgayKhoiHanh > DateTime.Now.AddHours(2))
                                    {
                                        <a href="@Url.Action("Cancel", "Ve", new { id = ticket.VeId })" 
                                           class="btn btn-sm btn-outline-danger"
                                           onclick="return confirm('Bạn có chắc chắn muốn hủy vé này?')">
                                            <i class="bi bi-x-circle me-1"></i>Hủy vé
                                        </a>
                                    }
                                    else if (ticket.ChuyenXe.NgayKhoiHanh > DateTime.Now)
                                    {
                                        <small class="text-muted">Không thể hủy<br/>(&lt; 2h khởi hành)</small>
                                    }
                                }
                                
                                @if (!string.IsNullOrEmpty(ticket.Email))
                                {
                                    <button class="btn btn-sm btn-outline-info mt-1" 
                                            onclick="resendEmail('@ticket.VeId')" 
                                            title="Gửi lại email xác nhận">
                                        <i class="bi bi-envelope me-1"></i>Gửi lại email
                                    </button>
                                }
                            </div>
                        </td>
                    </tr>
                }
            </tbody>
        </table>
    </div>
    
    <!-- Summary -->
    <div class="row mt-4">
        <div class="col-md-12">
            <div class="alert alert-info">
                <div class="row text-center">
                    <div class="col-md-3">
                        <strong>Tổng vé:</strong> @Model.Count
                    </div>
                    <div class="col-md-3">
                        <strong>Đã đặt:</strong> @Model.Count(v => v.TrangThai == DatVeXe.Models.TrangThaiVe.DaDat)
                    </div>
                    <div class="col-md-3">
                        <strong>Đã hủy:</strong> @Model.Count(v => v.TrangThai == DatVeXe.Models.TrangThaiVe.DaHuy)
                    </div>
                    <div class="col-md-3">
                        <strong>Tổng chi tiêu:</strong> @Model.Where(v => v.TrangThai == DatVeXe.Models.TrangThaiVe.DaDat).Sum(v => v.GiaVe).ToString("N0") VNĐ
                    </div>
                </div>
            </div>
        </div>
    </div>
}
else
{
    <div class="text-center py-4">
        <i class="bi bi-ticket-perforated text-muted" style="font-size: 3rem;"></i>
        <h5 class="mt-3 text-muted">Không có vé nào</h5>
        <p class="text-muted">Không tìm thấy vé nào trong danh mục này.</p>
    </div>
}

<script>
function resendEmail(ticketId) {
    if (confirm('Bạn có muốn gửi lại email xác nhận cho vé này?')) {
        // Implement resend email functionality
        fetch('/Ve/ResendEmail/' + ticketId, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Email đã được gửi lại thành công!');
            } else {
                alert('Có lỗi xảy ra khi gửi email: ' + data.message);
            }
        })
        .catch(error => {
            alert('Có lỗi xảy ra khi gửi email.');
        });
    }
}
</script>

<style>
    .table th {
        border-top: none;
        font-weight: 600;
        color: #495057;
    }
    
    .table td {
        vertical-align: middle;
    }
    
    .btn-group-vertical .btn {
        margin-bottom: 2px;
    }
    
    .badge {
        font-size: 0.75em;
    }
    
    .alert-info {
        background-color: #e3f2fd;
        border-color: #6CABDD;
        color: #0d47a1;
    }
</style>
