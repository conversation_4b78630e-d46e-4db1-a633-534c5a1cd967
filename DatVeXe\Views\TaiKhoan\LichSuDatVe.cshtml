@model List<DatVeXe.Models.Ve>
@{
    ViewData["Title"] = "Lịch sử đặt vé";
}

<div class="container-fluid mt-4">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <div class="user-avatar mb-3">
                        <i class="bi bi-person-circle" style="font-size: 4rem; color: #6CABDD;"></i>
                    </div>
                    <h5 class="card-title">@Context.Session.GetString("UserName")</h5>
                    <p class="text-muted">@Context.Session.GetString("UserEmail")</p>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0"><i class="bi bi-list me-2"></i>Menu</h6>
                </div>
                <div class="list-group list-group-flush">
                    <a href="@Url.Action("Dashboard", "TaiKhoan")" class="list-group-item list-group-item-action">
                        <i class="bi bi-speedometer2 me-2"></i>Dashboard
                    </a>
                    <a href="@Url.Action("LichSuDatVe", "TaiKhoan")" class="list-group-item list-group-item-action active">
                        <i class="bi bi-clock-history me-2"></i>Lịch sử đặt vé
                    </a>
                    <a href="@Url.Action("Profile", "TaiKhoan")" class="list-group-item list-group-item-action">
                        <i class="bi bi-person-gear me-2"></i>Cập nhật thông tin
                    </a>
                    <a href="@Url.Action("Create", "Ve")" class="list-group-item list-group-item-action">
                        <i class="bi bi-plus-circle me-2"></i>Đặt vé mới
                    </a>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9">
            <div class="card">
                <div class="card-header bg-light">
                    <h4 class="mb-0">
                        <i class="bi bi-clock-history me-2"></i>
                        Lịch sử đặt vé
                    </h4>
                </div>
                <div class="card-body">
                    @if (Model != null && Model.Any())
                    {
                        <!-- Filter Tabs -->
                        <ul class="nav nav-pills mb-4" id="filterTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="all-tab" data-bs-toggle="pill" data-bs-target="#all" type="button" role="tab">
                                    <i class="bi bi-list me-1"></i>Tất cả (@Model.Count)
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="upcoming-tab" data-bs-toggle="pill" data-bs-target="#upcoming" type="button" role="tab">
                                    <i class="bi bi-calendar-check me-1"></i>Sắp tới (@Model.Count(v => v.TrangThai == DatVeXe.Models.TrangThaiVe.DaDat && v.ChuyenXe.NgayKhoiHanh > DateTime.Now))
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="completed-tab" data-bs-toggle="pill" data-bs-target="#completed" type="button" role="tab">
                                    <i class="bi bi-check-circle me-1"></i>Đã hoàn thành (@Model.Count(v => v.TrangThai == DatVeXe.Models.TrangThaiVe.DaDat && v.ChuyenXe.NgayKhoiHanh <= DateTime.Now))
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="cancelled-tab" data-bs-toggle="pill" data-bs-target="#cancelled" type="button" role="tab">
                                    <i class="bi bi-x-circle me-1"></i>Đã hủy (@Model.Count(v => v.TrangThai == DatVeXe.Models.TrangThaiVe.DaHuy))
                                </button>
                            </li>
                        </ul>

                        <div class="tab-content" id="filterTabsContent">
                            <!-- All Tickets -->
                            <div class="tab-pane fade show active" id="all" role="tabpanel">
                                @await Html.PartialAsync("_TicketList", Model)
                            </div>

                            <!-- Upcoming Tickets -->
                            <div class="tab-pane fade" id="upcoming" role="tabpanel">
                                @await Html.PartialAsync("_TicketList", Model.Where(v => v.TrangThai == DatVeXe.Models.TrangThaiVe.DaDat && v.ChuyenXe.NgayKhoiHanh > DateTime.Now).ToList())
                            </div>

                            <!-- Completed Tickets -->
                            <div class="tab-pane fade" id="completed" role="tabpanel">
                                @await Html.PartialAsync("_TicketList", Model.Where(v => v.TrangThai == DatVeXe.Models.TrangThaiVe.DaDat && v.ChuyenXe.NgayKhoiHanh <= DateTime.Now).ToList())
                            </div>

                            <!-- Cancelled Tickets -->
                            <div class="tab-pane fade" id="cancelled" role="tabpanel">
                                @await Html.PartialAsync("_TicketList", Model.Where(v => v.TrangThai == DatVeXe.Models.TrangThaiVe.DaHuy).ToList())
                            </div>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="bi bi-ticket-perforated text-muted" style="font-size: 4rem;"></i>
                            <h4 class="mt-3 text-muted">Chưa có vé nào</h4>
                            <p class="text-muted">Bạn chưa đặt vé nào. Hãy đặt vé đầu tiên của bạn!</p>
                            <a href="@Url.Action("Create", "Ve")" class="btn btn-primary btn-lg">
                                <i class="bi bi-plus-circle me-2"></i>Đặt vé ngay
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .card {
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border: none;
    }
    
    .user-avatar {
        margin-bottom: 1rem;
    }
    
    .list-group-item {
        border: none;
        border-bottom: 1px solid #eee;
    }
    
    .list-group-item:hover {
        background-color: #f8f9fa;
    }
    
    .list-group-item.active {
        background-color: #6CABDD;
        border-color: #6CABDD;
    }
    
    .nav-pills .nav-link {
        border-radius: 20px;
        margin-right: 10px;
    }
    
    .nav-pills .nav-link.active {
        background-color: #6CABDD;
    }
    
    .border-primary { border-color: #6CABDD !important; }
    .text-primary { color: #6CABDD !important; }
    .bg-primary { background-color: #6CABDD !important; }
    .btn-primary { background-color: #6CABDD; border-color: #6CABDD; }
    .btn-primary:hover { background-color: #5a9bc4; border-color: #5a9bc4; }
</style>
