@model IEnumerable<DatVeXe.Models.Ve>

@{
    ViewData["Title"] = "Quản lý vé";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <h3 style="color: #2c3e50; font-weight: 600;">
        <i class="fas fa-ticket-alt" style="color: #27ae60;"></i>
        Quản lý vé
    </h3>
    <div class="btn-group">
        <button class="btn dropdown-toggle" type="button" data-bs-toggle="dropdown" style="border: 1px solid #7f8c8d; color: #7f8c8d;">
            <i class="fas fa-filter"></i>
            Lọc theo trạng thái
        </button>
        <ul class="dropdown-menu">
            <li><a class="dropdown-item" href="#">Tất cả</a></li>
            <li><a class="dropdown-item" href="#">Đã đặt</a></li>
            <li><a class="dropdown-item" href="#"><PERSON><PERSON> hủy</a></li>
            <li><a class="dropdown-item" href="#">Đã sử dụng</a></li>
        </ul>
    </div>
</div>

<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead style="background-color: #34495e; color: white;">
                    <tr>
                        <th>Mã vé</th>
                        <th>Khách hàng</th>
                        <th>Tuyến đường</th>
                        <th>Ngày đi</th>
                        <th>Số ghế</th>
                        <th>Giá vé</th>
                        <th>Ngày đặt</th>
                        <th>Trạng thái</th>
                        <th>Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var ve in Model)
                    {
                        <tr>
                            <td>@ve.VeId</td>
                            <td>@ve.NguoiDung?.HoTen</td>
                            <td>@ve.ChuyenXe?.TuyenDuong?.TenTuyen</td>
                            <td>@ve.ChuyenXe?.NgayKhoiHanh.ToString("dd/MM/yyyy")</td>
                            <td>@ve.ChoNgoi?.SoGhe</td>
                            <td>@ve.GiaVe.ToString("N0") VNĐ</td>
                            <td>@ve.NgayDat.ToString("dd/MM/yyyy HH:mm")</td>
                            <td>
                                @switch (ve.TrangThai)
                                {
                                    case TrangThaiVe.DaDat:
                                        <span class="badge" style="background-color: #27ae60; color: white;">Đã đặt</span>
                                        break;
                                    case TrangThaiVe.DaHuy:
                                        <span class="badge" style="background-color: #e74c3c; color: white;">Đã hủy</span>
                                        break;
                                    case TrangThaiVe.DaSuDung:
                                        <span class="badge" style="background-color: #3498db; color: white;">Đã sử dụng</span>
                                        break;
                                    case TrangThaiVe.DaHoanTien:
                                        <span class="badge" style="background-color: #f39c12; color: white;">Đã hoàn tiền</span>
                                        break;
                                    default:
                                        <span class="badge" style="background-color: #95a5a6; color: white;">@ve.TrangThai</span>
                                        break;
                                }
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm" style="border: 1px solid #3498db; color: #3498db;">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    @if (ve.TrangThai == TrangThaiVe.DaDat)
                                    {
                                        <button type="button" class="btn btn-sm" style="border: 1px solid #27ae60; color: #27ae60;">
                                            <i class="fas fa-check"></i>
                                        </button>
                                    }
                                    <button type="button" class="btn btn-sm" style="border: 1px solid #e74c3c; color: #e74c3c;">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>

<div class="mt-3">
    <small class="text-muted">
        Tổng cộng: @Model.Count() vé
    </small>
</div>
