@model DatVeXe.Models.Ve
@{
    ViewData["Title"] = "Chi tiết vé";
}
<div class="container py-4">
    <h1>@ViewData["Title"]</h1>

    @if (TempData["Error"] != null)
    {
        <div class="alert alert-danger">
            @TempData["Error"]
        </div>
    }

    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">Thông tin vé xe</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6 class="card-subtitle mb-3">Thông tin khách hàng</h6>
                    <dl class="row">
                        <dt class="col-sm-4">Tên khách:</dt>
                        <dd class="col-sm-8">@Model.TenKhach</dd>

                        <dt class="col-sm-4"><PERSON><PERSON> điện thoại:</dt>
                        <dd class="col-sm-8">@Model.SoDienThoai</dd>

                        <dt class="col-sm-4">Ngày đặt:</dt>
                        <dd class="col-sm-8">@Model.NgayDat.ToString("dd/MM/yyyy HH:mm")</dd>
                    </dl>
                </div>
                <div class="col-md-6">
                    <h6 class="card-subtitle mb-3">Thông tin chuyến xe</h6>
                    <dl class="row">
                        <dt class="col-sm-4">Điểm đi:</dt>
                        <dd class="col-sm-8">@Model.ChuyenXe.DiemDi</dd>

                        <dt class="col-sm-4">Điểm đến:</dt>
                        <dd class="col-sm-8">@Model.ChuyenXe.DiemDen</dd>

                        <dt class="col-sm-4">Ngày khởi hành:</dt>
                        <dd class="col-sm-8">@Model.ChuyenXe.NgayKhoiHanh.ToString("dd/MM/yyyy HH:mm")</dd>

                        <dt class="col-sm-4">Biển số xe:</dt>
                        <dd class="col-sm-8">@Model.ChuyenXe.Xe.BienSo</dd>

                        <dt class="col-sm-4">Loại xe:</dt>
                        <dd class="col-sm-8">@Model.ChuyenXe.Xe.LoaiXe</dd>

                        <dt class="col-sm-4">Số ghế:</dt>
                        <dd class="col-sm-8">@Model.ChuyenXe.Xe.SoGhe chỗ</dd>
                    </dl>
                </div>
            </div>
        </div>
        <div class="card-footer">
            <div class="btn-group">
                <a asp-action="Index" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Quay lại
                </a>
                @if (Model.ChuyenXe.NgayKhoiHanh > DateTime.Now)
                {
                    <a asp-action="Edit" asp-route-id="@Model.VeId" class="btn btn-warning">
                        <i class="fas fa-edit"></i> Sửa
                    </a>
                    <a asp-action="Delete" asp-route-id="@Model.VeId" class="btn btn-danger">
                        <i class="fas fa-trash"></i> Xóa
                    </a>
                }
            </div>
        </div>
    </div>
</div>

@section Styles {
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        .btn-group .btn {
            margin-right: 5px;
        }
        .btn-group .btn:last-child {
            margin-right: 0;
        }
        dt {
            font-weight: 600;
        }
    </style>
}
