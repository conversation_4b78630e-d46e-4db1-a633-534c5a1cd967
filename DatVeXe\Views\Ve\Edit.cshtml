@model DatVeXe.Models.Ve

@{
    ViewData["Title"] = "Sửa vé";
}

<div class="container py-4">
    <h1>@ViewData["Title"]</h1>

    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">Thông tin vé xe</h5>
        </div>
        <div class="card-body">
            <form asp-action="Edit" method="post">
                <div asp-validation-summary="ModelOnly" class="alert alert-danger"></div>
                <input type="hidden" asp-for="VeId" />
                <input type="hidden" asp-for="NgayDat" />

                <div class="row">
                    <div class="col-md-6">
                        <h6 class="card-subtitle mb-3">Thông tin khách hàng</h6>
                        
                        <div class="mb-3">
                            <label asp-for="TenKhach" class="form-label">Tên khách</label>
                            <input asp-for="TenKhach" class="form-control" />
                            <span asp-validation-for="TenKhach" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="SoDienThoai" class="form-label">Số điện thoại</label>
                            <input asp-for="SoDienThoai" class="form-control" />
                            <span asp-validation-for="SoDienThoai" class="text-danger"></span>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <h6 class="card-subtitle mb-3">Thông tin chuyến xe</h6>
                        
                        <div class="mb-3">
                            <label asp-for="ChuyenXeId" class="form-label">Chọn chuyến xe</label>
                            <select asp-for="ChuyenXeId" asp-items="ViewBag.ChuyenXeList" class="form-select">
                                <option value="">-- Chọn chuyến xe --</option>
                            </select>
                            <span asp-validation-for="ChuyenXeId" class="text-danger"></span>
                        </div>
                    </div>
                </div>

                <div class="card-footer bg-transparent px-0 pb-0">
                    <div class="btn-group">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Lưu
                        </button>
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Quay lại
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

@section Styles {
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        .btn-group .btn {
            margin-right: 5px;
        }
        .btn-group .btn:last-child {
            margin-right: 0;
        }
    </style>
}

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
