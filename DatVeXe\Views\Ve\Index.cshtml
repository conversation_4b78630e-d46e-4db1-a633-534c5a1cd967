@model DatVeXe.Models.QuanLyVeViewModel
@{
    ViewData["Title"] = ViewBag.IsAdmin == true ? "Quản lý vé" : "<PERSON>h sách vé của tôi";
}

<!-- Hero Section -->
<div class="tickets-hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="tickets-hero-title">
                    <i class="bi bi-ticket-perforated me-3"></i>
                    @ViewData["Title"]
                </h1>
                <p class="tickets-hero-subtitle">
                    @if (ViewBag.IsAdmin == true)
                    {
                        <span>Quản lý tất cả vé trong hệ thống</span>
                    }
                    else
                    {
                        <span>Quản lý và theo dõi các vé xe đã đặt</span>
                    }
                </p>
            </div>
            <div class="col-lg-4 text-end">
                <a asp-action="Create" class="btn btn-primary btn-lg">
                    <i class="bi bi-plus-circle me-2"></i>Đặt vé mới
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="tickets-content">
    <div class="container py-5">
        <!-- Alerts -->
        @if (TempData["Success"] != null)
        {
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="bi bi-check-circle me-2"></i>
                @TempData["Success"]
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        }

        @if (TempData["Error"] != null)
        {
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="bi bi-exclamation-triangle me-2"></i>
                @TempData["Error"]
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        }

        @if (ViewBag.IsAdmin == true)
        {
            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card border-primary text-center">
                        <div class="card-body">
                            <i class="bi bi-ticket-perforated text-primary" style="font-size: 2rem;"></i>
                            <h4 class="mt-2">@Model.TongVe</h4>
                            <p class="text-muted">Tổng vé</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-success text-center">
                        <div class="card-body">
                            <i class="bi bi-check-circle text-success" style="font-size: 2rem;"></i>
                            <h4 class="mt-2">@Model.VeDaDat</h4>
                            <p class="text-muted">Đã đặt</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-danger text-center">
                        <div class="card-body">
                            <i class="bi bi-x-circle text-danger" style="font-size: 2rem;"></i>
                            <h4 class="mt-2">@Model.VeDaHuy</h4>
                            <p class="text-muted">Đã hủy</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-warning text-center">
                        <div class="card-body">
                            <i class="bi bi-currency-dollar text-warning" style="font-size: 2rem;"></i>
                            <h4 class="mt-2">@Model.DoanhThuThucTe.ToString("N0")</h4>
                            <p class="text-muted">Doanh thu thực (VNĐ)</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filter Form -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="bi bi-funnel me-2"></i>Bộ lọc tìm kiếm
                    </h6>
                </div>
                <div class="card-body">
                    <form method="get" asp-action="Index">
                        <div class="row g-3">
                            <div class="col-md-2">
                                <label class="form-label">Trạng thái</label>
                                <select name="trangThai" class="form-select">
                                    <option value="">Tất cả</option>
                                    <option value="@((int)DatVeXe.Models.TrangThaiVe.DaDat)" selected="@(Model.TrangThaiFilter == DatVeXe.Models.TrangThaiVe.DaDat)">Đã đặt</option>
                                    <option value="@((int)DatVeXe.Models.TrangThaiVe.DaHuy)" selected="@(Model.TrangThaiFilter == DatVeXe.Models.TrangThaiVe.DaHuy)">Đã hủy</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Từ ngày</label>
                                <input type="date" name="tuNgay" class="form-control" value="@Model.TuNgay?.ToString("yyyy-MM-dd")">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Đến ngày</label>
                                <input type="date" name="denNgay" class="form-control" value="@Model.DenNgay?.ToString("yyyy-MM-dd")">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Điểm đi</label>
                                <input type="text" name="diemDi" class="form-control" placeholder="Điểm đi" value="@Model.DiemDi">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Điểm đến</label>
                                <input type="text" name="diemDen" class="form-control" placeholder="Điểm đến" value="@Model.DiemDen">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Mã vé</label>
                                <input type="text" name="maVe" class="form-control" placeholder="Mã vé" value="@Model.MaVe">
                            </div>
                        </div>
                        <div class="row g-3 mt-2">
                            <div class="col-md-3">
                                <label class="form-label">Tên khách</label>
                                <input type="text" name="tenKhach" class="form-control" placeholder="Tên khách hàng" value="@Model.TenKhach">
                            </div>
                            <div class="col-md-3">
                                <button type="submit" class="btn btn-primary mt-4">
                                    <i class="bi bi-search me-1"></i>Tìm kiếm
                                </button>
                                <a href="@Url.Action("Index")" class="btn btn-secondary mt-4 ms-2">
                                    <i class="bi bi-arrow-clockwise me-1"></i>Đặt lại
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        }

        <!-- Tickets Table -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">
                    <i class="bi bi-list-ul me-2"></i>Danh sách vé đã đặt
                </h5>
            </div>
            <div class="card-body p-0">
                @if (!Model.Ves.Any())
                {
                    <div class="text-center py-5">
                        <i class="bi bi-ticket-perforated text-muted" style="font-size: 4rem;"></i>
                        <h4 class="text-muted mt-3">Chưa có vé nào được đặt</h4>
                        <p class="text-muted">Hãy đặt vé đầu tiên của bạn!</p>
                        <a asp-action="Create" class="btn btn-primary btn-lg mt-3">
                            <i class="bi bi-plus-circle me-2"></i>Đặt vé ngay
                        </a>
                    </div>
                }
                else
                {
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th class="border-0">
                                        <i class="bi bi-hash me-1"></i>Mã vé
                                    </th>
                                    <th class="border-0">
                                        <i class="bi bi-person me-1"></i>Tên khách
                                    </th>
                                    @if (ViewBag.IsAdmin == true)
                                    {
                                        <th class="border-0">
                                            <i class="bi bi-envelope me-1"></i>Email
                                        </th>
                                    }
                                    <th class="border-0">
                                        <i class="bi bi-telephone me-1"></i>Số điện thoại
                                    </th>
                                    <th class="border-0">
                                        <i class="bi bi-geo-alt me-1"></i>Tuyến xe
                                    </th>
                                    <th class="border-0">
                                        <i class="bi bi-calendar me-1"></i>Ngày khởi hành
                                    </th>
                                    <th class="border-0">
                                        <i class="bi bi-currency-dollar me-1"></i>Giá vé
                                    </th>
                                    <th class="border-0">
                                        <i class="bi bi-info-circle me-1"></i>Trạng thái
                                    </th>
                                    <th class="border-0">
                                        <i class="bi bi-clock me-1"></i>Ngày đặt
                                    </th>
                                    <th class="border-0">
                                        <i class="bi bi-gear me-1"></i>Thao tác
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var ve in Model.Ves)
                                {
                                    var timeUntilDeparture = ve.ChuyenXe.NgayKhoiHanh - DateTime.Now;
                                    var canCancel = timeUntilDeparture.TotalHours > 2;
                                    var isExpired = ve.ChuyenXe.NgayKhoiHanh <= DateTime.Now;

                                    <tr class="@(isExpired ? "table-secondary" : "") @(ve.TrangThai == DatVeXe.Models.TrangThaiVe.DaHuy ? "table-warning" : "")">
                                        <td class="align-middle">
                                            <div class="fw-bold text-primary">@ve.MaVe</div>
                                        </td>
                                        <td class="align-middle">
                                            <div class="fw-bold">@ve.TenKhach</div>
                                        </td>
                                        @if (ViewBag.IsAdmin == true)
                                        {
                                            <td class="align-middle">
                                                <small class="text-muted">@(string.IsNullOrEmpty(ve.Email) ? "Không có" : ve.Email)</small>
                                            </td>
                                        }
                                        <td class="align-middle">
                                            <span class="badge bg-light text-dark">@ve.SoDienThoai</span>
                                        </td>
                                        <td class="align-middle">
                                            <div class="d-flex align-items-center">
                                                <span class="fw-bold text-primary">@ve.ChuyenXe.DiemDi</span>
                                                <i class="bi bi-arrow-right mx-2 text-muted"></i>
                                                <span class="fw-bold text-success">@ve.ChuyenXe.DiemDen</span>
                                            </div>
                                            <small class="text-muted">@ve.ChuyenXe.Xe.BienSo - @ve.ChuyenXe.Xe.LoaiXe</small>
                                        </td>
                                        <td class="align-middle">
                                            <div class="fw-bold">@ve.ChuyenXe.NgayKhoiHanh.ToString("dd/MM/yyyy")</div>
                                            <small class="text-muted">@ve.ChuyenXe.NgayKhoiHanh.ToString("HH:mm")</small>
                                        </td>
                                        <td class="align-middle">
                                            <span class="fw-bold text-success">@ve.GiaVe.ToString("N0") VNĐ</span>
                                        </td>
                                        <td class="align-middle">
                                            @if (ve.TrangThai == DatVeXe.Models.TrangThaiVe.DaDat)
                                            {
                                                <span class="badge bg-success">
                                                    <i class="bi bi-check-circle me-1"></i>Đã đặt
                                                </span>
                                            }
                                            else if (ve.TrangThai == DatVeXe.Models.TrangThaiVe.DaHuy)
                                            {
                                                <span class="badge bg-danger">
                                                    <i class="bi bi-x-circle me-1"></i>Đã hủy
                                                </span>
                                                @if (ve.NgayHuy.HasValue)
                                                {
                                                    <br><small class="text-muted">@ve.NgayHuy.Value.ToString("dd/MM/yyyy")</small>
                                                }
                                            }
                                        </td>
                                        <td class="align-middle">
                                            <div>@ve.NgayDat.ToString("dd/MM/yyyy")</div>
                                            <small class="text-muted">@ve.NgayDat.ToString("HH:mm")</small>
                                        </td>
                                        <td class="align-middle">
                                            <div class="btn-group" role="group">
                                                <a asp-action="Details" asp-route-id="@ve.VeId" class="btn btn-outline-info btn-sm" title="Xem chi tiết">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                @if (!isExpired)
                                                {
                                                    <a asp-action="Edit" asp-route-id="@ve.VeId" class="btn btn-outline-warning btn-sm" title="Chỉnh sửa">
                                                        <i class="bi bi-pencil"></i>
                                                    </a>
                                                    @if (canCancel)
                                                    {
                                                        <a asp-action="Cancel" asp-route-id="@ve.VeId" class="btn btn-outline-danger btn-sm" title="Hủy vé">
                                                            <i class="bi bi-x-circle"></i>
                                                        </a>
                                                    }
                                                    else
                                                    {
                                                        <button class="btn btn-outline-secondary btn-sm" disabled title="Không thể hủy vé trong vòng 2 giờ trước khởi hành">
                                                            <i class="bi bi-ban"></i>
                                                        </button>
                                                    }
                                                    @if (Context.Session.GetInt32("IsAdmin") == 1)
                                                    {
                                                        <a asp-action="Delete" asp-route-id="@ve.VeId" class="btn btn-outline-danger btn-sm" title="Xóa vé">
                                                            <i class="bi bi-trash"></i>
                                                        </a>
                                                    }
                                                }
                                                else
                                                {
                                                    <span class="badge bg-secondary">Đã khởi hành</span>
                                                }
                                            </div>
                                        </td>
                                    </tr>
                    }
                                }
                            </tbody>
                        </table>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

@section Styles {
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        /* Override dark background for this page */
        body {
            background: #f8f9fa !important;
            color: #212529 !important;
        }

        /* Hero Section */
        .tickets-hero-section {
            background: linear-gradient(135deg, #6CABDD 0%, #5A91BA 100%);
            padding: 3rem 0;
            margin-bottom: 0;
        }

        .tickets-hero-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #fff;
            margin-bottom: 1rem;
        }

        .tickets-hero-subtitle {
            font-size: 1.2rem;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 0;
        }

        /* Content Area */
        .tickets-content {
            background: #f8f9fa;
            min-height: 60vh;
        }

        /* Card Styling */
        .card {
            border-radius: 15px;
            border: none;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .card-header {
            background: #fff;
            border-bottom: 2px solid #f1f3f4;
            border-radius: 15px 15px 0 0 !important;
            padding: 1.5rem;
        }

        .card-title {
            color: #0a2640;
            font-weight: 600;
        }

        /* Table Styling */
        .table {
            color: #212529;
        }

        .table thead th {
            background: #f8f9fa;
            color: #495057;
            font-weight: 600;
            border-bottom: 2px solid #dee2e6;
            padding: 1rem 0.75rem;
        }

        .table tbody td {
            padding: 1rem 0.75rem;
            vertical-align: middle;
            border-bottom: 1px solid #f1f3f4;
        }

        .table tbody tr:hover {
            background-color: #f8f9fa;
        }

        .table-secondary {
            background-color: #f1f3f4 !important;
            opacity: 0.7;
        }

        /* Button Styling */
        .btn-primary {
            background: linear-gradient(135deg, #6CABDD 0%, #5A91BA 100%);
            border: none;
            font-weight: 600;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(108, 171, 221, 0.3);
        }

        .btn-group .btn {
            margin-right: 0.25rem;
            border-radius: 6px;
        }

        .btn-group .btn:last-child {
            margin-right: 0;
        }

        .btn-sm {
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
        }

        /* Badge Styling */
        .badge {
            font-size: 0.8rem;
            padding: 0.5rem 0.75rem;
            border-radius: 6px;
        }

        /* Alert Styling */
        .alert {
            border-radius: 10px;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        /* Empty State */
        .text-center i {
            opacity: 0.5;
        }

        /* Responsive */
        @@media (max-width: 768px) {
            .tickets-hero-title {
                font-size: 2rem;
            }

            .btn-group {
                flex-direction: column;
                width: 100%;
            }

            .btn-group .btn {
                margin-right: 0;
                margin-bottom: 0.25rem;
                width: 100%;
            }
        }
    </style>
}

@section Scripts {
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css" />
}
